import asyncio
import threading
from queue import Empty, Queue
from typing import List

import webview
from lightweight_charts import abstract
from lightweight_charts.util import FLOAT, parse_event_message


"""
Architecture of lightweight_charts:
Python Code → WebviewHandler → Queue → PyWV Process → webview.create_window()
"""


class CallbackAPI:
    """Handles callbacks from Javascript to Python. No changes needed here."""

    def __init__(self, emit_queue: Queue):
        self.emit_queue = emit_queue

    def callback(self, message: str):
        self.emit_queue.put(message)


class WebviewHandler:
    """
    A simplified handler that no longer manages threads. It acts as a direct,
    thread-safe interface to the pywebview API.
    """

    def __init__(self):
        self.windows: List[webview.Window] = []
        self.emit_queue = Queue()  # For JS -> Python callbacks
        self.api = CallbackAPI(self.emit_queue)

    def create_window(self, width, height, x, y, screen, on_top, maximize, title):
        """
        Directly creates a pywebview window. This method is thread-safe.
        The window object itself is returned, not an ID.
        """
        screen_obj = webview.screens[screen] if screen is not None else None
        if maximize:
            active_screen = screen_obj or webview.screens[0]
            width, height = active_screen.width, active_screen.height

        # We pass the `js_api` instance to handle callbacks.
        window = webview.create_window(
            title,
            url=abstract.INDEX,
            js_api=self.api,
            width=width,
            height=height,
            x=x,
            y=y,
            screen=screen_obj,
            on_top=on_top,
            background_color="#FFFFFF",
        )
        self.windows.append(window)

    def exit(self):
        """Destroys all open windows."""
        for window in self.windows:
            window.destroy()
        self.windows.clear()


class TerraFinChart(abstract.AbstractChart):
    _main_window_handlers = None
    WV: WebviewHandler = WebviewHandler()

    def __init__(
        self,
        width: int = 800,
        height: int = 600,
        x: int = None,
        y: int = None,
        title: str = "",
        screen: int = None,
        on_top: bool = False,
        maximize: bool = False,
        debug: bool = False,
        toolbox: bool = False,
        inner_width: float = 1.0,
        inner_height: float = 1.0,
        scale_candles_only: bool = False,
        position: FLOAT = "left",
    ):
        # Store debug flag for later use in webview.start()
        self.debug = debug
        self.is_alive = True

        # Create the window and store the object directly
        self.WV.create_window(width, height, x, y, screen, on_top, maximize, title)
        self.window = self.WV.windows[0]

        # The `loaded` event will be used to signal when the chart is ready
        self.loaded = threading.Event()
        self.window.events.loaded += self.on_load

        # The abstract.Window now gets passed the window object itself
        chart_window = abstract.Window(script_func=self.window.evaluate_js, js_api_code="pywebview.api.callback")
        # Note: The return queue is no longer needed as `evaluate_js` is now blocking and returns directly.
        # abstract.Window._return_q = self.WV.return_queue

        if TerraFinChart._main_window_handlers is None:
            super().__init__(chart_window, inner_width, inner_height, scale_candles_only, toolbox, position=position)
            TerraFinChart._main_window_handlers = self.win.handlers
        else:
            chart_window.handlers = TerraFinChart._main_window_handlers
            super().__init__(chart_window, inner_width, inner_height, scale_candles_only, toolbox, position=position)

    def on_load(self):
        """Called when the webview has loaded its content."""
        self.win.on_js_load()
        self.loaded.set()

    def _event_loop(self):
        """
        This function runs in a background thread managed by pywebview.
        It polls the emit_queue for events from the Javascript frontend.
        """
        # We need an asyncio event loop to run async callbacks from this sync thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        while self.is_alive:
            try:
                response = self.WV.emit_queue.get(timeout=0.05)
                if response == "exit":
                    self.is_alive = False
                    self.WV.exit()
                    break

                func, args = parse_event_message(self.win, response)
                if asyncio.iscoroutinefunction(func):
                    # Run the async function from our synchronous thread
                    future = asyncio.run_coroutine_threadsafe(func(*args), loop)
                    future.result()  # Wait for completion
                else:
                    func(*args)
            except Empty:
                continue  # Nothing in the queue, loop again.
        loop.close()

    def show(self, block: bool = True):
        """
        Shows the chart window. This method MUST be called from the main thread.
        :param block: If True (default), this call will block until the chart window is closed.
        """
        # Make the window visible before starting the event loop
        self.window.show()

        # Wait for the JS to be loaded before starting the event loop
        self.loaded.wait()

        if block:
            # This call takes over the main thread for the GUI event loop.
            # We pass `_event_loop` to be run concurrently in a background thread.
            webview.start(func=self._event_loop, debug=self.debug)

        # When `webview.start()` finishes, the window has been closed.
        self.is_alive = False

    def exit(self):
        """Closes and destroys the chart window."""
        self.is_alive = False
        self.window.destroy()
