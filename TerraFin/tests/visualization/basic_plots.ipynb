from TerraFin.data_factory import DataFactory


factory = DataFactory()
# df = factory.get_economic_data("Money Multiplier")
df = factory.get_market_data("Nasdaq")
# df = factory.get_fred_data("T10Y2Y")

from TerraFin.visualization import TerraFinChart


chart = TerraFinChart()
chart.set(df)
chart.show()

from lightweight_charts import Jupyter<PERSON>hart


chart = JupyterChart()
chart.set(df)
chart.load()

from lightweight_charts.widgets import Streamlit<PERSON>hart


chart = Streamlit<PERSON>hart(width=900, height=600)
chart.set(df)
chart.load()

from lightweight_charts import Chart


chart = Chart(debug=True)
chart.set(df)
chart.show(block=True)